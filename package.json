{"name": "nuxt-campus", "version": "1.0.0", "private": true, "scripts": {"dev": "cross-env NODE_ENV=dev nuxt", "prod": "cross-env NODE_ENV=prod nuxt", "build": "cross-env NODE_ENV=prod nuxt build", "start": "cross-env NODE_ENV=prod nuxt start", "generate": "cross-env NODE_ENV=prod nuxt generate"}, "dependencies": {"axios": "^0.26.0", "clipboard": "^2.0.10", "core-js": "^3.19.3", "cross-env": "^7.0.3", "element-ui": "^2.15.6", "file-saver": "2.0.5", "js-cookie": "^3.0.1", "nuxt": "^2.15.8", "video.js": "^7.17.0", "vue": "^2.6.14", "vue-cropper": "^0.5.10", "vue-server-renderer": "^2.6.14", "vue-template-compiler": "^2.6.14", "webpack": "^4.46.0"}, "devDependencies": {"svg-sprite-loader": "^6.0.11"}}