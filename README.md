<p align="center"><a href="https://oddfar.com/" target="_blank" rel="noopener noreferrer"><img width="180" src="https://note.oddfar.com/img/web.png" alt="logo"></a></p>

<p align="center">
  <a href="https://github.com/oddfar/nuxt_campus_example/stargazers"><img src="https://img.shields.io/github/stars/oddfar/nuxt_campus_example.svg"></a>
	<a href="https://github.com/oddfar/nuxt_campus_example/blob/master/LICENSE"><img src="https://img.shields.io/github/license/mashape/apistatus.svg"></a>
</p>



<h2 align="center">nuxt_campus_example</h2>

<p align="center">校园信息墙、表白墙、万能墙 </p>

<p align="center">网页web端，基于nuxt </p>

## 项目介绍

此项目使用 **Campus** 进行编写：<https://github.com/oddfar/campus>

> **Campus** 一款简单的后台管理系统，快速开发框架，适合大学生开发毕设，或其他小项目。

本项目为编写的一个例子：校园信息墙、表白墙、万能墙

- 在线预览：暂无

- 项目文档：<https://oddfar.github.io/campus-doc/campus-example>

### 项目代码

|                | GitHub                                          |
| -------------- | ----------------------------------------------- |
| 后端           | <https://github.com/oddfar/campus-example>      |
| nuxt web端     | <https://github.com/oddfar/nuxt_campus_example> |
| uni-app 移动端 | <https://github.com/oddfar/uni-app_campus_web>  |

vue后台代码在后端项目中

## 演示图

请 [点击此处](https://oddfar.github.io/campus-doc/campus-example/#%E6%BC%94%E7%A4%BA%E5%9B%BE) 访问文档查看
